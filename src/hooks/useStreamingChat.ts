import { useState, useCallback, useRef, useEffect } from 'react';
import React from 'react';

// 定义消息类型
interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  reasoning?: string; // 深度思考模型的推理内容
}

interface UseStreamingChatProps {
  maxHistoryMessages?: number;
}

interface UseStreamingChatReturn {
  messages: ChatMessage[];
  isStreaming: boolean;
  sendMessage: (message: string) => Promise<void>;
  stopStreaming: () => void;
  error: string | null;
  setMessages: React.Dispatch<React.SetStateAction<ChatMessage[]>>;
}

export const useStreamingChat = ({
  maxHistoryMessages = 3
}: UseStreamingChatProps = {}): UseStreamingChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isActivated, setIsActivated] = useState<boolean>(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // 检查应用激活状态
  useEffect(() => {
    const checkActivation = async () => {
      try {
        const result = await window.electronAPI.checkActivation();
        setIsActivated(result.activated);
      } catch (err) {
        console.error('检查激活状态失败:', err);
        setIsActivated(false);
      }
    };
    
    checkActivation();
    
    // 监听激活状态变化
    const unsubscribeActivationSuccess = window.electronAPI.onActivationSuccess(() => {
      setIsActivated(true);
    });
    
    const unsubscribeActivationRequired = window.electronAPI.onActivationRequired(() => {
      setIsActivated(false);
    });
    
    return () => {
      unsubscribeActivationSuccess();
      unsubscribeActivationRequired();
    };
  }, []);
  
  // 终止当前流
  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsStreaming(false);
  }, []);

  // 发送消息并获取流式响应
  const sendMessage = useCallback(async (message: string) => {
    if (isStreaming) {
      stopStreaming();
    }
    
    setError(null);
    
    // 检查应用是否已激活
    if (!isActivated) {
      try {
        // 再次检查激活状态（可能在此期间已激活）
        const result = await window.electronAPI.checkActivation();
        setIsActivated(result.activated);
        
        if (!result.activated) {
          setError('应用未激活，请先激活应用后再使用语音聊天功能');
          return;
        }
      } catch (err) {
        console.error("检查激活状态失败:", err);
        setError('检查激活状态失败，请确保应用已激活');
        return;
      }
    }
    
    // 添加用户消息到历史
    const userMessage: ChatMessage = { role: 'user', content: message };
    
    // 创建新的消息历史，确保UI立即更新显示用户消息
    const newHistory = [...messages, userMessage];
    setMessages(newHistory);
    
    // 创建助手回复的占位
    const assistantMessage: ChatMessage = { role: 'assistant', content: '', reasoning: '' };
    
    // 更新消息列表，添加助手消息占位符
    setMessages([...newHistory, assistantMessage]);
    setIsStreaming(true);
    
    try {
      abortControllerRef.current = new AbortController();
      
      // 获取有限的历史消息，保持对话上下文但限制长度
      // 先计算对话轮次（一轮是一对用户和助手消息）
      const conversationPairs: ChatMessage[] = [];
      // 从最后往前遍历，获取最近的maxHistoryMessages轮对话
      let pairCount = 0;
      for (let i = newHistory.length - 1; i >= 0; i--) {
        conversationPairs.unshift(newHistory[i]);
        if (newHistory[i].role === 'user') {
          pairCount++;
          if (pairCount >= maxHistoryMessages) break;
        }
      }
      
      // 确保将当前用户消息加入上下文
      if (conversationPairs.length > 0 && conversationPairs[conversationPairs.length - 1].role !== 'user') {
        conversationPairs.push(userMessage);
      }
      
      console.log('发送消息:', message);
      console.log(`当前消息历史总数: ${newHistory.length}, 限制为最近${maxHistoryMessages}轮对话`);
      console.log(`实际发送的上下文消息数: ${conversationPairs.length}`);
      
      // 直接在这里创建一个引用，以便在回调中使用
      let currentResponseContent = '';
      let currentReasoningContent = '';

      // 添加调试用的消息更新计数
      let updateCount = 0;

      const eventSource = await window.electronAPI.streamChat(
        conversationPairs,
        (data: { type: 'content' | 'reasoning', token: string }) => {
          updateCount++;

          // 根据类型更新不同的内容
          if (data.type === 'content') {
            currentResponseContent += data.token;
          } else if (data.type === 'reasoning') {
            currentReasoningContent += data.token;
          }

          // 强制更新整个消息，而不是增量更新
          setMessages(prevMessages => {
            // 创建一个全新的消息数组
            const newMessages = [...prevMessages];
            // 找到最后一条助手消息
            const lastIndex = newMessages.length - 1;
            if (lastIndex >= 0 && newMessages[lastIndex].role === 'assistant') {
              // 完全替换最后一条消息
              newMessages[lastIndex] = {
                role: 'assistant',
                content: currentResponseContent,
                reasoning: currentReasoningContent
              };
            } else {
              console.warn(`无法找到要更新的助手消息，当前消息数量: ${newMessages.length}`);
              // 如果没找到助手消息，添加一个新的
              newMessages.push({
                role: 'assistant',
                content: currentResponseContent,
                reasoning: currentReasoningContent
              });
              console.log(`添加新助手消息(${updateCount}), 当前消息数量: ${newMessages.length}`);
            }
            return newMessages;
          });
        }
      );

      // 设置事件监听器
      eventSource.addEventListener('message', (event: { data: any; }) => {
        // console.log('收到message事件:', event.data);
      });

      eventSource.addEventListener('done', () => {
        console.log(`流式响应完成，共收到${updateCount}条更新，最终回复长度: ${currentResponseContent.length}，推理内容长度: ${currentReasoningContent.length}`);
        setIsStreaming(false);
        abortControllerRef.current = null;
      });

      eventSource.addEventListener('error', (error: Error) => {
        console.error("流式对话错误:", error);
        setError(error.message || '流式对话发生错误');
        setIsStreaming(false);
        abortControllerRef.current = null;
      });
      
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error("发送消息失败:", errorMessage);
      setError(`发送消息失败: ${errorMessage}`);
      setIsStreaming(false);
    }
  }, [messages, isStreaming, stopStreaming, maxHistoryMessages, isActivated]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    messages,
    isStreaming,
    sendMessage,
    stopStreaming,
    error,
    setMessages
  };
}; 