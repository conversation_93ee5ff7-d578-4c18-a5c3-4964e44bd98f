import React, { useEffect, useRef } from 'react';
import ChatMessage from './ChatMessage';
import { useTheme } from '../../contexts/theme';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  reasoning?: string; // 深度思考模型的推理内容
}

interface ChatHistoryProps {
  messages: ChatMessage[];
  className?: string;
}

const ChatHistory: React.FC<ChatHistoryProps> = ({ messages, className = '' }) => {
  const { theme } = useTheme();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  
  // 滚动到底部（使用防抖避免频繁滚动）
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      // 使用requestAnimationFrame确保DOM更新完成后再滚动
      requestAnimationFrame(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      });
    }
  };

  // 当消息更新时自动滚动到底部（优化防抖时间）
  useEffect(() => {
    const scrollTimeout = setTimeout(scrollToBottom, 50); // 50ms防抖，更快响应
    return () => clearTimeout(scrollTimeout);
  }, [messages]);
  
  // 添加自定义滚动条样式
  useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* 自定义滚动条样式 */
      .voice-chat-container::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      .voice-chat-container::-webkit-scrollbar-track {
        background: ${theme === 'dark' ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255, 255, 255, 0.1)'};
        border-radius: 4px;
      }
      .voice-chat-container::-webkit-scrollbar-thumb {
        background: ${theme === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'};
        border-radius: 4px;
      }
      .voice-chat-container::-webkit-scrollbar-thumb:hover {
        background: ${theme === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)'};
      }
    `;
    document.head.appendChild(styleEl);

    return () => {
      document.head.removeChild(styleEl);
    };
  }, [theme]);
  
  // 处理鼠标滚轮事件
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (chatContainerRef.current) {
        e.preventDefault();
        chatContainerRef.current.scrollTop += e.deltaY;
      }
    };
    
    const container = chatContainerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
    }
    
    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
      }
    };
  }, []);
  
  return (
    <div
      ref={chatContainerRef}
      className={`flex-1 overflow-y-auto overflow-x-hidden p-4 voice-chat-container min-h-0 ${className}`}
      style={{
        minHeight: '200px', // 确保最小高度
        maxHeight: 'calc(100vh - 300px)' // 确保不会超出视口
      }}
    >
      <div className="w-full max-w-full">
        {messages.map((message, index) => (
          <ChatMessage
            key={index}
            role={message.role}
            content={message.content}
            reasoning={message.reasoning}
          />
        ))}
        {/* 用于自动滚动的空div */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default ChatHistory; 