// ScreenshotHelper.ts

import path from "node:path"
import fs from "node:fs"
import { app } from "electron"
import { v4 as uuidv4 } from "uuid"
import { execFile } from "child_process"
import { promisify } from "util"
import screenshot from "screenshot-desktop"
import os from "os"

const execFileAsync = promisify(execFile)

// PowerShell路径缓存和检测
class PowerShellPathManager {
  private static cachedPath: string | null = null
  private static isChecking = false

  /**
   * 获取PowerShell可执行文件路径
   */
  static async getPowerShellPath(): Promise<string | null> {
    // 如果已经缓存了路径，直接返回
    if (this.cachedPath) {
      return this.cachedPath
    }

    // 如果正在检查中，等待检查完成
    if (this.isChecking) {
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!this.isChecking) {
            clearInterval(checkInterval)
            resolve(this.cachedPath)
          }
        }, 100)
      })
    }

    this.isChecking = true

    try {
      // 尝试多种PowerShell路径
      const possiblePaths = this.getPossiblePowerShellPaths()

      for (const psPath of possiblePaths) {
        try {
          console.log(`尝试PowerShell路径: ${psPath}`)

          // 测试PowerShell是否可用
          await execFileAsync(psPath, ['-NoProfile', '-Command', 'Write-Host "test"'], {
            timeout: 5000,
            windowsHide: true
          })

          console.log(`找到可用的PowerShell路径: ${psPath}`)
          this.cachedPath = psPath
          return psPath
        } catch (error) {
          console.log(`PowerShell路径不可用: ${psPath}, 错误:`, error.message)
          continue
        }
      }

      console.warn("未找到可用的PowerShell路径")
      return null
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 获取所有可能的PowerShell路径
   */
  private static getPossiblePowerShellPaths(): string[] {
    const paths: string[] = []

    // 1. 直接使用命令名（如果在PATH中）
    paths.push('powershell')
    paths.push('pwsh') // PowerShell Core

    // 2. Windows系统默认路径
    const systemRoot = process.env.SystemRoot || 'C:\\Windows'
    paths.push(path.join(systemRoot, 'System32', 'WindowsPowerShell', 'v1.0', 'powershell.exe'))
    paths.push(path.join(systemRoot, 'SysWOW64', 'WindowsPowerShell', 'v1.0', 'powershell.exe'))

    // 3. PowerShell Core可能的安装路径
    const programFiles = process.env['ProgramFiles'] || 'C:\\Program Files'
    const programFilesX86 = process.env['ProgramFiles(x86)'] || 'C:\\Program Files (x86)'

    paths.push(path.join(programFiles, 'PowerShell', '7', 'pwsh.exe'))
    paths.push(path.join(programFiles, 'PowerShell', '6', 'pwsh.exe'))
    paths.push(path.join(programFilesX86, 'PowerShell', '7', 'pwsh.exe'))
    paths.push(path.join(programFilesX86, 'PowerShell', '6', 'pwsh.exe'))

    // 4. 用户安装的PowerShell Core
    const userProfile = process.env.USERPROFILE
    if (userProfile) {
      paths.push(path.join(userProfile, 'AppData', 'Local', 'Microsoft', 'powershell', 'pwsh.exe'))
    }

    // 5. 通过注册表查找（如果可能）
    try {
      const registryPaths = this.getPathsFromRegistry()
      paths.push(...registryPaths)
    } catch (error) {
      console.log("无法从注册表获取PowerShell路径:", error.message)
    }

    // 去重并过滤存在的文件
    const uniquePaths = [...new Set(paths)]
    return uniquePaths.filter(p => {
      try {
        // 对于直接命令名，不检查文件存在性
        if (!p.includes('\\') && !p.includes('/')) {
          return true
        }
        return fs.existsSync(p)
      } catch {
        return false
      }
    })
  }

  /**
   * 尝试从注册表获取PowerShell路径
   */
  private static getPathsFromRegistry(): string[] {
    const paths: string[] = []

    try {
      // 这里可以添加注册表查询逻辑
      // 由于Node.js没有内置注册表支持，这里暂时返回空数组
      // 在实际实现中，可以使用第三方库如 'winreg' 来查询注册表
    } catch (error) {
      console.log("注册表查询失败:", error)
    }

    return paths
  }

  /**
   * 清除缓存的路径（用于测试或重新检测）
   */
  static clearCache(): void {
    this.cachedPath = null
  }
}

export class ScreenshotHelper {
  private screenshotQueue: string[] = []
  private extraScreenshotQueue: string[] = []
  private readonly MAX_SCREENSHOTS = 5

  private readonly screenshotDir: string
  private readonly extraScreenshotDir: string
  private readonly tempDir: string

  private view: "queue" | "solutions" | "debug" | "cheatsheet" = "queue"

  constructor(view: "queue" | "solutions" | "debug" | "cheatsheet" = "queue") {
    this.view = view

    // Initialize directories
    this.screenshotDir = path.join(app.getPath("userData"), "screenshots")
    this.extraScreenshotDir = path.join(
      app.getPath("userData"),
      "extra_screenshots"
    )
    this.tempDir = path.join(app.getPath("temp"), "interview-artifact-screenshots")

    // Create directories if they don't exist
    this.ensureDirectoriesExist();
    
    // Clean existing screenshot directories when starting the app
    this.cleanScreenshotDirectories();
  }
  
  private ensureDirectoriesExist(): void {
    const directories = [this.screenshotDir, this.extraScreenshotDir, this.tempDir];
    
    for (const dir of directories) {
      if (!fs.existsSync(dir)) {
        try {
          fs.mkdirSync(dir, { recursive: true });
          console.log(`Created directory: ${dir}`);
        } catch (err) {
          console.error(`Error creating directory ${dir}:`, err);
        }
      }
    }
  }
  
  // This method replaces loadExistingScreenshots() to ensure we start with empty queues
  private cleanScreenshotDirectories(): void {
    try {
      // Clean main screenshots directory
      if (fs.existsSync(this.screenshotDir)) {
        const files = fs.readdirSync(this.screenshotDir)
          .filter(file => file.endsWith('.png'))
          .map(file => path.join(this.screenshotDir, file));
        
        // Delete each screenshot file
        for (const file of files) {
          try {
            fs.unlinkSync(file);
            console.log(`Deleted existing screenshot: ${file}`);
          } catch (err) {
            console.error(`Error deleting screenshot ${file}:`, err);
          }
        }
      }
      
      // Clean extra screenshots directory
      if (fs.existsSync(this.extraScreenshotDir)) {
        const files = fs.readdirSync(this.extraScreenshotDir)
          .filter(file => file.endsWith('.png'))
          .map(file => path.join(this.extraScreenshotDir, file));
        
        // Delete each screenshot file
        for (const file of files) {
          try {
            fs.unlinkSync(file);
            console.log(`Deleted existing extra screenshot: ${file}`);
          } catch (err) {
            console.error(`Error deleting extra screenshot ${file}:`, err);
          }
        }
      }
      
      console.log("Screenshot directories cleaned successfully");
    } catch (err) {
      console.error("Error cleaning screenshot directories:", err);
    }
  }

  public getView(): "queue" | "solutions" | "debug" | "cheatsheet" {
    return this.view
  }

  public setView(view: "queue" | "solutions" | "debug" | "cheatsheet"): void {
    console.log("Setting view in ScreenshotHelper:", view)
    console.log(
      "Current queues - Main:",
      this.screenshotQueue,
      "Extra:",
      this.extraScreenshotQueue
    )
    this.view = view
  }

  public getScreenshotQueue(): string[] {
    return this.screenshotQueue
  }

  public getExtraScreenshotQueue(): string[] {
    console.log("Getting extra screenshot queue:", this.extraScreenshotQueue)
    return this.extraScreenshotQueue
  }

  public clearQueues(): void {
    // Clear screenshotQueue
    this.screenshotQueue.forEach((screenshotPath) => {
      fs.unlink(screenshotPath, (err) => {
        if (err)
          console.error(`Error deleting screenshot at ${screenshotPath}:`, err)
      })
    })
    this.screenshotQueue = []

    // Clear extraScreenshotQueue
    this.extraScreenshotQueue.forEach((screenshotPath) => {
      fs.unlink(screenshotPath, (err) => {
        if (err)
          console.error(
            `Error deleting extra screenshot at ${screenshotPath}:`,
            err
          )
      })
    })
    this.extraScreenshotQueue = []
  }

  private async captureScreenshot(): Promise<Buffer> {
    try {
      console.log("Starting screenshot capture...");
      
      // For Windows, try multiple methods
      if (process.platform === 'win32') {
        return await this.captureWindowsScreenshot();
      }

      // For macOS and Linux, use buffer directly
      console.log("Taking screenshot on non-Windows platform");
      const buffer = await screenshot({ format: 'png' });
      console.log(`Screenshot captured successfully, size: ${buffer.length} bytes`);
      return buffer;
    } catch (error) {
      console.error("Error capturing screenshot:", error);
      throw new Error(`Failed to capture screenshot: ${error.message}`);
    }
  }

  /**
   * Windows-specific screenshot capture with multiple fallback mechanisms
   */
  private async captureWindowsScreenshot(): Promise<Buffer> {
    console.log("Attempting Windows screenshot with multiple methods");

    // Method 1: Try screenshot-desktop with filename first
    try {
      const tempFile = path.join(this.tempDir, `temp-${uuidv4()}.png`);
      console.log(`Taking Windows screenshot to temp file (Method 1): ${tempFile}`);

      await screenshot({ filename: tempFile });

      if (fs.existsSync(tempFile)) {
        const buffer = await fs.promises.readFile(tempFile);
        console.log(`Method 1 successful, screenshot size: ${buffer.length} bytes`);

        // Cleanup temp file
        try {
          await fs.promises.unlink(tempFile);
        } catch (cleanupErr) {
          console.warn("Failed to clean up temp file:", cleanupErr);
        }

        return buffer;
      } else {
        console.log("Method 1 failed: File not created");
        throw new Error("Screenshot file not created");
      }
    } catch (error) {
      console.warn("Windows screenshot Method 1 failed:", error);

      // Method 2: Try using PowerShell with enhanced path detection
      try {
        console.log("Attempting Windows screenshot with PowerShell (Method 2)");
        return await this.captureWithPowerShell();
      } catch (psError) {
        console.warn("Windows PowerShell screenshot failed:", psError);

        // Method 3: Try alternative Windows API methods
        try {
          console.log("Attempting Windows screenshot with alternative methods (Method 3)");
          return await this.captureWithAlternativeMethods();
        } catch (altError) {
          console.warn("Alternative screenshot methods failed:", altError);

          // Method 4: Last resort - create a tiny placeholder image
          console.log("All screenshot methods failed, creating placeholder image");

          // Create a 1x1 transparent PNG as fallback
          const fallbackBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=', 'base64');
          console.log("Created placeholder image as fallback");

          // Show the error but return a valid buffer so the app doesn't crash
          throw new Error("Could not capture screenshot with any method. Please check your Windows security settings and try again.");
        }
      }
    }
  }

  /**
   * 使用PowerShell进行截图，支持多种PowerShell路径
   */
  private async captureWithPowerShell(): Promise<Buffer> {
    const tempFile = path.join(this.tempDir, `ps-temp-${uuidv4()}.png`);

    // 获取PowerShell路径
    const powerShellPath = await PowerShellPathManager.getPowerShellPath();
    if (!powerShellPath) {
      throw new Error("PowerShell not found on this system");
    }

    console.log(`Using PowerShell at: ${powerShellPath}`);

    // PowerShell command to take screenshot using .NET classes
    const psScript = `
    try {
      Add-Type -AssemblyName System.Windows.Forms,System.Drawing
      $screens = [System.Windows.Forms.Screen]::AllScreens
      $top = ($screens | ForEach-Object {$_.Bounds.Top} | Measure-Object -Minimum).Minimum
      $left = ($screens | ForEach-Object {$_.Bounds.Left} | Measure-Object -Minimum).Minimum
      $width = ($screens | ForEach-Object {$_.Bounds.Right} | Measure-Object -Maximum).Maximum
      $height = ($screens | ForEach-Object {$_.Bounds.Bottom} | Measure-Object -Maximum).Maximum
      $bounds = [System.Drawing.Rectangle]::FromLTRB($left, $top, $width, $height)
      $bmp = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
      $graphics = [System.Drawing.Graphics]::FromImage($bmp)
      $graphics.CopyFromScreen($bounds.Left, $bounds.Top, 0, 0, $bounds.Size)
      $bmp.Save('${tempFile.replace(/\\/g, '\\\\')}', [System.Drawing.Imaging.ImageFormat]::Png)
      $graphics.Dispose()
      $bmp.Dispose()
      Write-Host "Screenshot saved successfully"
    } catch {
      Write-Error "Failed to capture screenshot: $_"
      exit 1
    }
    `;

    // Execute PowerShell with enhanced error handling
    try {
      const { stdout, stderr } = await execFileAsync(powerShellPath, [
        '-NoProfile',
        '-ExecutionPolicy', 'Bypass',
        '-WindowStyle', 'Hidden',
        '-Command', psScript
      ], {
        timeout: 15000, // 15秒超时
        windowsHide: true
      });

      console.log("PowerShell output:", stdout);
      if (stderr) {
        console.warn("PowerShell stderr:", stderr);
      }
    } catch (execError) {
      console.error("PowerShell execution failed:", execError);
      throw new Error(`PowerShell execution failed: ${execError.message}`);
    }

    // Check if file exists and read it
    if (fs.existsSync(tempFile)) {
      const buffer = await fs.promises.readFile(tempFile);
      console.log(`PowerShell screenshot successful, size: ${buffer.length} bytes`);

      // Cleanup
      try {
        await fs.promises.unlink(tempFile);
      } catch (err) {
        console.warn("Failed to clean up PowerShell temp file:", err);
      }

      return buffer;
    } else {
      throw new Error("PowerShell screenshot file not created");
    }
  }

  /**
   * 使用替代方法进行截图
   */
  private async captureWithAlternativeMethods(): Promise<Buffer> {
    // 方法1: 尝试使用cmd + powershell
    try {
      console.log("Trying cmd + powershell method");
      const tempFile = path.join(this.tempDir, `cmd-ps-temp-${uuidv4()}.png`);

      const psCommand = `powershell -NoProfile -ExecutionPolicy Bypass -Command "Add-Type -AssemblyName System.Windows.Forms,System.Drawing; $screens = [System.Windows.Forms.Screen]::AllScreens; $top = ($screens | ForEach-Object {$_.Bounds.Top} | Measure-Object -Minimum).Minimum; $left = ($screens | ForEach-Object {$_.Bounds.Left} | Measure-Object -Minimum).Minimum; $width = ($screens | ForEach-Object {$_.Bounds.Right} | Measure-Object -Maximum).Maximum; $height = ($screens | ForEach-Object {$_.Bounds.Bottom} | Measure-Object -Maximum).Maximum; $bounds = [System.Drawing.Rectangle]::FromLTRB($left, $top, $width, $height); $bmp = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height; $graphics = [System.Drawing.Graphics]::FromImage($bmp); $graphics.CopyFromScreen($bounds.Left, $bounds.Top, 0, 0, $bounds.Size); $bmp.Save('${tempFile.replace(/\\/g, '\\\\')}', [System.Drawing.Imaging.ImageFormat]::Png); $graphics.Dispose(); $bmp.Dispose()"`;

      await execFileAsync('cmd', ['/c', psCommand], {
        timeout: 15000,
        windowsHide: true
      });

      if (fs.existsSync(tempFile)) {
        const buffer = await fs.promises.readFile(tempFile);
        console.log(`CMD+PowerShell screenshot successful, size: ${buffer.length} bytes`);

        try {
          await fs.promises.unlink(tempFile);
        } catch (err) {
          console.warn("Failed to clean up cmd temp file:", err);
        }

        return buffer;
      }
    } catch (cmdError) {
      console.warn("CMD+PowerShell method failed:", cmdError);
    }

    // 方法2: 尝试使用wmic (Windows Management Instrumentation)
    // 注意：wmic在较新的Windows版本中可能不可用
    try {
      console.log("Trying WMIC method (limited functionality)");
      // WMIC不能直接截图，但可以用来检测显示器信息
      // 这里主要是为了演示多种尝试方法的思路
      throw new Error("WMIC method not implemented for screenshot capture");
    } catch (wmicError) {
      console.warn("WMIC method failed:", wmicError);
    }

    throw new Error("All alternative screenshot methods failed");
  }

  public async takeScreenshot(
    hideMainWindow: () => void,
    showMainWindow: () => void
  ): Promise<string> {
    console.log("Taking screenshot in view:", this.view)
    hideMainWindow()
    
    // Increased delay for window hiding on Windows
    const hideDelay = process.platform === 'win32' ? 500 : 300;
    await new Promise((resolve) => setTimeout(resolve, hideDelay))

    let screenshotPath = ""
    try {
      // Get screenshot buffer using cross-platform method
      const screenshotBuffer = await this.captureScreenshot();
      
      if (!screenshotBuffer || screenshotBuffer.length === 0) {
        throw new Error("Screenshot capture returned empty buffer");
      }

      // Save and manage the screenshot based on current view
      if (this.view === "queue") {
        screenshotPath = path.join(this.screenshotDir, `${uuidv4()}.png`)
        await fs.promises.writeFile(screenshotPath, screenshotBuffer)
        console.log("Adding screenshot to main queue:", screenshotPath)
        this.screenshotQueue.push(screenshotPath)
        if (this.screenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.screenshotQueue.shift()
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath)
              console.log(
                "Removed old screenshot from main queue:",
                removedPath
              )
            } catch (error) {
              console.error("Error removing old screenshot:", error)
            }
          }
        }
      } else {
        // In solutions view, only add to extra queue
        screenshotPath = path.join(this.extraScreenshotDir, `${uuidv4()}.png`)
        await fs.promises.writeFile(screenshotPath, screenshotBuffer)
        console.log("Adding screenshot to extra queue:", screenshotPath)
        this.extraScreenshotQueue.push(screenshotPath)
        if (this.extraScreenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.extraScreenshotQueue.shift()
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath)
              console.log(
                "Removed old screenshot from extra queue:",
                removedPath
              )
            } catch (error) {
              console.error("Error removing old screenshot:", error)
            }
          }
        }
      }
    } catch (error) {
      console.error("Screenshot error:", error)
      throw error
    } finally {
      // Increased delay for showing window again
      await new Promise((resolve) => setTimeout(resolve, 200))
      showMainWindow()
    }

    return screenshotPath
  }

  public async getImagePreview(filepath: string): Promise<string> {
    try {
      if (!fs.existsSync(filepath)) {
        console.error(`Image file not found: ${filepath}`);
        return '';
      }
      
      const data = await fs.promises.readFile(filepath)
      return `data:image/png;base64,${data.toString("base64")}`
    } catch (error) {
      console.error("Error reading image:", error)
      return ''
    }
  }

  public async deleteScreenshot(
    path: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (fs.existsSync(path)) {
        await fs.promises.unlink(path)
      }
      
      if (this.view === "queue") {
        this.screenshotQueue = this.screenshotQueue.filter(
          (filePath) => filePath !== path
        )
      } else {
        this.extraScreenshotQueue = this.extraScreenshotQueue.filter(
          (filePath) => filePath !== path
        )
      }
      return { success: true }
    } catch (error) {
      console.error("Error deleting file:", error)
      return { success: false, error: error.message }
    }
  }

  public clearExtraScreenshotQueue(): void {
    // Clear extraScreenshotQueue
    this.extraScreenshotQueue.forEach((screenshotPath) => {
      if (fs.existsSync(screenshotPath)) {
        fs.unlink(screenshotPath, (err) => {
          if (err)
            console.error(
              `Error deleting extra screenshot at ${screenshotPath}:`,
              err
            )
        })
      }
    })
    this.extraScreenshotQueue = []
  }

  /**
   * 预检查PowerShell可用性（用于应用启动时的诊断）
   */
  public async checkPowerShellAvailability(): Promise<{
    available: boolean;
    path?: string;
    version?: string;
    error?: string;
  }> {
    try {
      console.log("开始检查PowerShell可用性...");

      const powerShellPath = await PowerShellPathManager.getPowerShellPath();
      if (!powerShellPath) {
        return {
          available: false,
          error: "未找到PowerShell可执行文件"
        };
      }

      // 获取PowerShell版本信息
      try {
        const { stdout } = await execFileAsync(powerShellPath, [
          '-NoProfile',
          '-Command',
          '$PSVersionTable.PSVersion.ToString()'
        ], {
          timeout: 5000,
          windowsHide: true
        });

        const version = stdout.trim();
        console.log(`PowerShell可用，版本: ${version}, 路径: ${powerShellPath}`);

        return {
          available: true,
          path: powerShellPath,
          version: version
        };
      } catch (versionError) {
        console.warn("无法获取PowerShell版本，但路径可用:", versionError);
        return {
          available: true,
          path: powerShellPath,
          version: "未知版本"
        };
      }
    } catch (error) {
      console.error("PowerShell可用性检查失败:", error);
      return {
        available: false,
        error: error.message
      };
    }
  }

  /**
   * 清除PowerShell路径缓存（用于重新检测）
   */
  public clearPowerShellCache(): void {
    PowerShellPathManager.clearCache();
    console.log("PowerShell路径缓存已清除");
  }

  /**
   * 获取截图功能的诊断信息
   */
  public async getScreenshotDiagnostics(): Promise<{
    platform: string;
    screenshotDesktopAvailable: boolean;
    powerShellInfo: {
      available: boolean;
      path?: string;
      version?: string;
      error?: string;
    };
    tempDirWritable: boolean;
    tempDirPath: string;
  }> {
    const diagnostics = {
      platform: process.platform,
      screenshotDesktopAvailable: false,
      powerShellInfo: await this.checkPowerShellAvailability(),
      tempDirWritable: false,
      tempDirPath: this.tempDir
    };

    // 检查screenshot-desktop库是否可用
    try {
      // 尝试获取显示器信息（不实际截图）
      await screenshot.listDisplays();
      diagnostics.screenshotDesktopAvailable = true;
    } catch (error) {
      console.warn("screenshot-desktop库不可用:", error);
    }

    // 检查临时目录是否可写
    try {
      const testFile = path.join(this.tempDir, `test-${Date.now()}.txt`);
      await fs.promises.writeFile(testFile, 'test');
      await fs.promises.unlink(testFile);
      diagnostics.tempDirWritable = true;
    } catch (error) {
      console.warn("临时目录不可写:", error);
    }

    return diagnostics;
  }
}
